import { useState, useCallback } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import useGlobalStore from '../../services/globalState';
import { authSelectors } from '../../services/authService';

export const useProtectedAction = () => {
    const firebaseUser: FirebaseUser | null = useGlobalStore(authSelectors.firebaseUser);

    // State for the dialog that will be controlled by this hook
    const [signUpDialogData, setSignUpDialogData] = useState<{
        visible: boolean;
        featureName?: string;
        title?: string;
        icon?: string;
        description?: string;
    }>({ visible: false });

    const protectAction = useCallback(
        <T extends (...args: any[]) => void>(
            action: T,
            featureName?: string,
            title?: string,
            icon?: string,
            description?: string
        ) => {
            return (...args: Parameters<T>): void => {
                if (firebaseUser?.isAnonymous) {
                    // User is anonymous, show prompt
                    setSignUpDialogData({ visible: true, featureName, title, icon, description });
                } else if (!firebaseUser) {
                    // No user at all (e.g., anonymous sign-in failed, or a brief state)
                    // Also treat as needing to sign up/log in
                    setSignUpDialogData({ visible: true, featureName, title, icon, description });
                } else {
                    // User is logged in with a permanent account
                    action(...args);
                }
            };
        },
        [firebaseUser] // Re-create protectAction if firebaseUser changes
    );

    const closeSignUpDialog = useCallback(() => {
        setSignUpDialogData((prev) => ({ ...prev, visible: false }));
    }, []);

    return {
        protectAction,
        signUpDialogState: signUpDialogData, // Pass this state to your SignUpPromptDialog
        closeSignUpDialog,                  // Pass this to dismiss the dialog
    };
};