import { BasicPlace } from "../../models/place";
import { SearchSuggestion } from "../../models/suggestion";
import { apiFetchClient } from "./apiHttpClient"; // Assuming apiFetchClient returns the client function
import { ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE, CONTENT_TYPE_HEADER, DELETE_METHOD, GET_METHOD, PATCH_METHOD, POST_METHOD, PUT_METHOD } from "./constants";
import { PharmaciesSearchRequest } from "./models/pharmaciesSearchRequest";
import { AddSavedPlaceTagRequest, SavedPlace, SavedPlaceCreationRequest, SavedPlaceUpdateRequest } from "./models/savedPlace";
import { UserRegistrationRequest } from "./models/userRegistrationRequest";
import { UserUpdateRequest } from "./models/userUpdateRequest";

export class ApiService {

    public registerUser: (userRegistrationRequest: UserRegistrationRequest) => Promise<Response>;
    public updateUser: (userUid: string, userUpdateRequest: UserUpdateRequest) => Promise<Response>;
    public deleteUser: (userUid: string) => Promise<Response>;
    public getUserDetails: (userUid: string) => Promise<Response>;

    public addUserSavedPlace: (userUid: string, request: SavedPlaceCreationRequest) => Promise<SavedPlace>;
    public addSavedPlaceTag: (userUid: string, savedPlaceId: string, request: AddSavedPlaceTagRequest) => Promise<SavedPlace>;
    public updateUserSavedPlace: (userUid: string, savedPlaceId: string, request: SavedPlaceUpdateRequest) => Promise<SavedPlace>;
    public deleteUserSavedPlace: (userUid: string, savedPlaceId: string) => Promise<SavedPlace>;
    public deleteHiddenExistingPharmacies: (userUid: string) => Promise<void>;

    public getSearchSuggestions: (input: string) => Promise<ReadonlyArray<SearchSuggestion>>;
    public getPlaceDetailsByPlaceId: (placeId: string) => Promise<BasicPlace>;

    public searchPharmacies: (searchRequest: PharmaciesSearchRequest) => Promise<Response>;

    constructor() {
        this.updateUser = async (userUid: string, userUpdateRequest: UserUpdateRequest) => {
            const url: string = `/users/${userUid}`;
            return apiFetchClient(url, PUT_METHOD, new Map([[CONTENT_TYPE_HEADER, APPLICATION_JSON_MEDIA_TYPE]]), userUpdateRequest);
        };

        this.registerUser = async (userRegistrationRequest: UserRegistrationRequest): Promise<Response> => {
            const url = `/users`;
            return apiFetchClient(
                url,
                POST_METHOD,
                new Map([[CONTENT_TYPE_HEADER, APPLICATION_JSON_MEDIA_TYPE]]),
                userRegistrationRequest
            );
        };

        this.searchPharmacies = async (searchRequest: PharmaciesSearchRequest): Promise<Response> => {
            const url = `/pharmacies`;
            return apiFetchClient(
                url,
                POST_METHOD,
                new Map([
                    [ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE],
                    [CONTENT_TYPE_HEADER, APPLICATION_JSON_MEDIA_TYPE]
                ]),
                searchRequest
            );
        };

        this.getSearchSuggestions = async (input: string): Promise<ReadonlyArray<SearchSuggestion>> => {
            const url = `/places?input=${input}`;
            return apiFetchClient(url, GET_METHOD, new Map([[ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE]]))
                .then(response => response.json());
        };

        this.getPlaceDetailsByPlaceId = async (placeId: string): Promise<BasicPlace> => {
            const url = `/places/${placeId}`;
            return apiFetchClient(url, GET_METHOD, new Map([[ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE]]))
                .then(response => response.json());
        };

        this.getUserDetails = async (userUid: string): Promise<Response> => {
            const url = `/users/${userUid}`;
            return apiFetchClient(url, GET_METHOD, new Map([[ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE]]));
        };

        this.deleteUser = async (userUid: string): Promise<Response> => {
            const url = `/users/${userUid}`;
            return apiFetchClient(url, DELETE_METHOD, new Map([[ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE]]));
        };

        this.addSavedPlaceTag = async (userUid: string, savedPlaceId: string, request: AddSavedPlaceTagRequest): Promise<SavedPlace> => {
            const url = `/users/${userUid}/saved-places/${savedPlaceId}/tags`;
            return apiFetchClient(
                url,
                POST_METHOD,
                new Map([
                    [ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE],
                    [CONTENT_TYPE_HEADER, APPLICATION_JSON_MEDIA_TYPE]
                ]),
                request
            ).then(response => response.json());
        };

        this.addUserSavedPlace = async (userUid: string, request: SavedPlaceCreationRequest) => {
            const url = `/users/${userUid}/saved-places`;
            return apiFetchClient(
                url,
                POST_METHOD,
                new Map([
                    [ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE],
                    [CONTENT_TYPE_HEADER, APPLICATION_JSON_MEDIA_TYPE]
                ]),
                request
            ).then(response => response.json());
        };

        this.updateUserSavedPlace = async (userUid: string, savedPlaceId: string, request: SavedPlaceUpdateRequest) => {
            const url = `/users/${userUid}/saved-places/${savedPlaceId}`;
            return apiFetchClient(
                url,
                PATCH_METHOD,
                new Map([
                    [ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE],
                    [CONTENT_TYPE_HEADER, APPLICATION_JSON_MEDIA_TYPE]
                ]),
                request
            ).then(response => response.json());
        };

        this.deleteUserSavedPlace = async (userUid: string, savedPlaceId: string): Promise<SavedPlace> => {
            const url = `/users/${userUid}/saved-places/${savedPlaceId}`;
            return apiFetchClient(url, DELETE_METHOD).then(response => response.json());
        };

        this.deleteHiddenExistingPharmacies = async (userUid: string): Promise<void> => {
            const url = `/users/${userUid}/saved-places?tags=HIDDEN&types=EXISTING_PHARMACY`;
            return apiFetchClient(
                url,
                DELETE_METHOD,
                new Map([[ACCEPT_HEADER, APPLICATION_JSON_MEDIA_TYPE]])
            ).then(response => {
                if (!response.ok) {
                    throw new Error("Failed to remove all tags from saved places.");
                }
            });
        };
    }
}
