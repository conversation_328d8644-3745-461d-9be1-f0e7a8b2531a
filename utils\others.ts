import { Region } from "react-native-maps";
import { PlaceType } from "../models/constants";
import { CustomPlace, Place } from "../models/place";
import { SavedPlace } from "../services/api/models/savedPlace";
import { RectangleArea } from "../services/api/models/pharmaciesSearchRequest";
import { Calendar, getCalendars, getLocales, Locale } from "expo-localization";

export const generateLatLng = (lat: number, lng: number): string => {
    return `${lat}, ${lng}`;
};

export const isPharmacyOrChainage = (place: Place | CustomPlace): boolean => {
    return (PlaceType.PHARMACY === place.type || PlaceType.CHAINAGE === place.type);
};

const fromStringToPlaceType = (placeTypeString: string): PlaceType => {
    if (placeTypeString === 'PHARMACY') {
        return PlaceType.PHARMACY
    } else if (placeTypeString === 'CHAINAGE') {
        return PlaceType.CHAINAGE
    } else if (placeTypeString === 'EXISTING_PHARMACY') {
        return PlaceType.EXISTING_PHARMACY
    } else if (placeTypeString === 'SEARCH_RESULT') {
        return PlaceType.SEARCH_RESULT
    } else {
        throw Error(`Can't map ${placeTypeString} to a PlaceType enum`);
    }
};

export const mapSavedPlaceToCustomPlace = (savedPlace: SavedPlace): CustomPlace => {
    return {
        id: savedPlace.id,
        geometry: {
            ...savedPlace.geometry
        },
        formattedAddress: savedPlace.formattedAddress,
        placeId: savedPlace.placeId,
        type: fromStringToPlaceType(savedPlace.type),
        name: savedPlace.name,
        note: savedPlace.note,
        tags: savedPlace.tags,
        createdAt: savedPlace.createdAt,
        updatedAt: savedPlace.updatedAt
    } as CustomPlace;
};

export const mapSavedPlaceToPlace = (savedPlace: SavedPlace): Place => {
    return {
        geometry: {
            ...savedPlace.geometry
        },
        placeId: savedPlace.placeId,
        type: fromStringToPlaceType(savedPlace.type),
        name: savedPlace.name
    } as Place;
};

export function requireNonNull<T>(x: T | null | undefined): T {
    if (x === null || x === undefined) {
        throw new Error("Value cannot be null or undefined");
    }
    return x;
};

export const generateGoogleMapsURL = (lat: number, lng: number, placeId?: string) => {
    return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}${placeId ? `&query_place_id=${placeId}` : ''}`;
};

export const isCustomPlace = (place: Place | CustomPlace) => 'id' in place;

export const trimOrUndefined = (input?: string | null): string | undefined => {
    const trimmed = input?.trim();
    return trimmed === "" ? undefined : trimmed;
}

export const getRectangleArea = (region: Region): RectangleArea => {
    const { latitude, longitude, latitudeDelta, longitudeDelta } = region;
    return {
        southWest: {
            lat: latitude - latitudeDelta / 2,
            lng: longitude - longitudeDelta / 2,
        },
        northEast: {
            lat: latitude + latitudeDelta / 2,
            lng: longitude + longitudeDelta / 2,
        }
    } as RectangleArea;
};

export const getDeviceTimezone = (): string | null => {
    const calendars: Calendar[] = getCalendars();
    return calendars.length > 0 ? calendars[0].timeZone : null;
};

export const getDeviceRegion = (): string | null => {
    const locales: Locale[] = getLocales();
    return locales.length > 0 ? locales[0].regionCode : null;
};