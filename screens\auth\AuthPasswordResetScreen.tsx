import { StackScreenProps } from '@react-navigation/stack';
import { getAuth, sendPasswordResetEmail } from "firebase/auth";
import { useFormik } from 'formik';
import React, { useContext, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Dialog, HelperText, Portal, Text, TextInput } from "react-native-paper";
import * as Yup from 'yup';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import { ScrollView } from 'react-native';

const AuthPasswordResetScreen: React.FC<StackScreenProps<any>> = ({ navigation }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const [showConfirmationDialog, setShowConfirmationDialog] = useState<boolean>(false);

    const auth = getAuth();

    const formik = useFormik({
        initialValues: {
            email: ''
        },
        validationSchema: Yup.object({
            email: Yup.string()
                .email(translationService.translate("EMAIL_VALID_ERROR"))
                .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, translationService.translate("EMAIL_VALID_ERROR"))
                .required(translationService.translate("REQUIRED_FIELD")),
        }),
        onSubmit: (values, formikActions) => {
            sendPasswordResetEmail(auth, values.email)
                .then(() => {
                    setShowConfirmationDialog(true);
                    formikActions.setSubmitting(false);
                });
        }
    });

    const isEmailInvalid: boolean = Boolean(formik.touched.email && formik.errors.email);

    return (
        <ScrollView>
            <Portal>
                <Dialog visible={showConfirmationDialog} dismissable={false}>
                    <Dialog.Icon icon="email-check" color='green' size={40} />
                    <Dialog.Title style={styles.textAlignCenter}>{translationService.translate("EMAIL_SENT_DIALOG_TITLE")}</Dialog.Title>
                    <Dialog.Content>
                        <Text variant="bodyLarge">{translationService.translate("EMAIL_SENT_DIALOG_DESCRIPTION")}</Text>
                    </Dialog.Content>
                    <Dialog.Actions>
                        <Button onPress={() => navigation.goBack()}>Ok</Button>
                    </Dialog.Actions>
                </Dialog>
            </Portal>
            <View style={styles.formDescription}>
                <Text variant='headlineSmall'>{translationService.translate("RESET_PASSWORD_TITLE")}</Text>
                <View>
                    <Text variant='bodyMedium'>{translationService.translate("RESET_PASSWORD_STEP_1")}</Text>
                    <Text variant='bodyMedium'>{translationService.translate("RESET_PASSWORD_STEP_2")}</Text>
                    <Text variant='bodyMedium'>{translationService.translate("RESET_PASSWORD_STEP_3")}</Text>
                </View>
                <Text variant='bodyMedium'>{translationService.translate("RESET_PASSWORD_EMAIL_NOTICE")}</Text>
                <View style={styles.formContainer}>
                    <View>
                        <TextInput
                            onChangeText={formik.handleChange('email')}
                            onBlur={formik.handleBlur('email')}
                            autoCapitalize="none"
                            keyboardType="email-address"
                            autoCorrect={false}
                            disabled={formik.isSubmitting}
                            value={formik.values.email}
                            error={isEmailInvalid}
                            mode="outlined"
                            label={translationService.translate("SHORT_EMAIL")}
                        />
                        {isEmailInvalid ? (
                            <HelperText type="error">{formik.errors.email}</HelperText>
                        ) : null}
                    </View>

                    <Button
                        mode="contained"
                        onPress={() => formik.handleSubmit()}
                        loading={formik.isSubmitting}
                    >
                        {translationService.translate("RESET_PASSWORD")}
                    </Button>
                </View>
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    formContainer: {
        gap: 16
    },
    textAlignCenter: {
        textAlign: 'center'
    },
    formDescription: {
        flexDirection: 'column',
        padding: 16,
        gap: 16
    }
});

export default AuthPasswordResetScreen;
