import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import React, { useContext, useEffect, useState } from "react";
import { Dimensions, StyleSheet, View, Image } from 'react-native';
import { Divider, List, ProgressBar, Searchbar, Text, useTheme } from "react-native-paper";
import { Geometry } from "../../models/geometry";
import { SearchSuggestion } from "../../models/suggestion";
import { ApiService } from "../../services/api/apiService";
import { apiServiceContext, translationServiceContext } from "../../services/provider";
import { TranslationService } from '../../services/translationService';
import { BasicPlace } from '../../models/place';

type SearchScreenProps = CompositeScreenProps<
    BottomTabScreenProps<HomeStackParamList, 'Search'>,
    NativeStackScreenProps<RootStackParamList>
>;

const DEBOUNCE_TIME_MS: number = 500;

const SearchScreen: React.FC<SearchScreenProps> = ({ navigation }) => {

    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const theme = useTheme();

    const [searchQuery, setSearchQuery] = useState<string>("");
    const [loading, setLoading] = useState<boolean>(false);

    const [querySuggestions, setQuerySuggestions] = useState<ReadonlyArray<SearchSuggestion>>([]);

    const getQuerySuggetions = async () => {
        try {
            const suggestions: ReadonlyArray<SearchSuggestion> = await apiService.getSearchSuggestions(searchQuery);
            setQuerySuggestions(suggestions);
        } finally {
            setLoading(false);
        }
    };

    const onSuggestionPress = async (placeId: string) => {
        setLoading(true);
        return apiService.getPlaceDetailsByPlaceId(placeId)
            .then((location: BasicPlace) => {
                navigation.navigate({
                    name: 'Home',
                    params: {
                        operation: 'SEARCH',
                        placeId: location.placeId,
                        geometry: {
                            lat: location.geometry.lat,
                            lng: location.geometry.lng
                        } as Geometry,
                        placeName: location.name
                    }
                });
            })
            .finally(() => {
                setLoading(false);
            });
    };

    useEffect(() => {
        if (searchQuery.trim().length === 0) {
            setLoading(false);
            setQuerySuggestions([]);
        } else {
            setLoading(true);
            // Set up the debouncing effect
            const handler = setTimeout(() => {
                getQuerySuggetions();
            }, DEBOUNCE_TIME_MS);
            // Cleanup function to cancel the timeout if searchQuery changes
            return () => {
                clearTimeout(handler);
                setLoading(false);
            };
        }
    }, [searchQuery]);

    return (
        <View>
            <Searchbar
                placeholder={translationService.translate("SEARCH_BAR_PLACEHOLDER")}
                mode="view"
                autoFocus
                focusable
                value={searchQuery}
                onChangeText={(query: string) => {
                    setSearchQuery(query)
                }}
                showDivider={false}
            />
            <ProgressBar visible={loading} indeterminate />
            <View>
                <List.Section>
                    <List.Subheader>{translationService.translate("SEARCH_SUGGESTIONS")}</List.Subheader>
                    {querySuggestions.map(({ placeId, name }) => (
                        <View key={placeId}>
                            <List.Item
                                title={name}
                                titleNumberOfLines={2}
                                onPress={() => onSuggestionPress(placeId)}
                                left={(props) => <List.Icon {...props} icon="map-marker-outline" />}
                            />
                            <Divider />
                        </View>
                    ))}

                    {/* Powered by Google attribution - shown when user has typed something */}
                    {searchQuery.trim().length > 0 && !(loading && querySuggestions.length === 0) && (
                        <View style={styles.poweredByContainer}>
                            <View style={styles.poweredByInlineContainer}>
                                <Text variant="bodyMedium" style={[styles.poweredByText]}>
                                    {translationService.translate("POWERED_BY")}
                                </Text>
                                <Image
                                    source={theme.dark
                                        ? require('../../assets/images/google_on_non_white.png')
                                        : require('../../assets/images/google_on_white.png')}
                                    style={styles.googleLogo}
                                    resizeMode="contain"
                                />
                            </View>
                        </View>
                    )}
                </List.Section>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    poweredByContainer: {
        padding: 8,
        marginTop: 8
    },
    poweredByInlineContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    poweredByText: {
        marginRight: 0,
        marginBottom: 4
    },
    googleLogo: {
        height: 18,
        width: 70,
        marginBottom: 0, // Slight adjustment to align with text baseline
    }
});

export default SearchScreen;
