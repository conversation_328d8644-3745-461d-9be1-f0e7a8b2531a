import { onAuthStateChanged, User, signOut } from 'firebase/auth';
import { auth } from '../config/firebase';
import { UserInfo } from '../models/userDetails';
import { ApiService } from './api/apiService';
import useGlobalStore, { GlobalStore } from './globalState';
import { RevenueCatService } from './revenueCatService';
import { UserDetails } from './api/models/userDetails';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Singleton instance to track initialization
let isInitialized = false;

export const authSelectors = {
    authenticatedUser: (state: GlobalStore) => {
        const firebaseUser: User | null  = state.firebaseUser;
        if (!firebaseUser) {
            console.error("[AuthSelectors] authenticatedUser: User is not authenticated");
            throw new Error("User is not authenticated");
        }
        return {
            userUid: firebaseUser.uid,
            isUserAnonymous: firebaseUser.isAnonymous
        };
    },
    authenticatedUserUid: (state: GlobalStore) => {
        const firebaseUserUid: string | undefined = state.firebaseUser?.uid;
        if (!firebaseUserUid) {
            console.error("[AuthSelectors] authenticatedUserUid: User is not authenticated");
            throw new Error("User is not authenticated");
        }
        return firebaseUserUid;
    },
    authenticatedUserDetails: (state: GlobalStore) => {
        const userDetails: UserInfo | null = state.user;
        if (!userDetails) {
            console.error("[AuthSelectors] authenticatedUserDetails: User is not authenticated");
            throw new Error("User is not authenticated");
        }
        return userDetails;
    },
    firebaseUser: (state: GlobalStore) => state.firebaseUser,
    userDetails: (state: GlobalStore) => state.user,
    authenticating: (state: GlobalStore) => state.authenticating
};

/**
 * Initializes the authentication service.
 * This should be called once at app startup.
 */
export function initializeAuthService(apiService: ApiService, revenueCatService: RevenueCatService): () => void {
    console.debug('[AuthService] initializeAuthService called.');
    if (isInitialized) {
        console.warn('[AuthService] Auth service already initialized. Skipping re-initialization.');
        return () => { }; // Return empty cleanup function
    }

    console.debug('[AuthService] Initializing auth service...');
    isInitialized = true;

    const setAuthenticating = async (loading: boolean) => {
        useGlobalStore.setState({ authenticating: loading });
    };

    const signInToRevenueCat = async (firebaseUser: User | null) => {
        if (!firebaseUser) {
            console.warn('[AuthService] signInToRevenueCat called with null user. Skipping.');
            return;
        }
        if (firebaseUser.isAnonymous) {
            console.debug('[AuthService] User is anonymous. Skipping RevenueCat login.');
            return;
        }
        console.debug(`[AuthService] Logging into RevenueCat for userUid: ${firebaseUser.uid}`);
        await revenueCatService.login(firebaseUser.uid);
        console.debug('[AuthService] Successfully logged into RevenueCat.');
    };

    const signOutFromRevenueCat = async () => {
        console.debug('[AuthService] Logging out of RevenueCat');
        await revenueCatService.logout();
        console.debug('[AuthService] Successfully logged out of RevenueCat.');
    };

    const clearState = async (store: GlobalStore, clearPharmacies: boolean, userUid?: string) => {
        await store.clearStateExceptPharmacies();
        console.debug('[AuthService] Cleared state except pharmacies.');
        if (clearPharmacies && userUid) {
            console.debug(`[AuthService] Clearing pharmacies for userUid: ${userUid}`);
            store.clearPharmacies(userUid);
            console.debug('[AuthService] Cleared pharmacies.');
        }
    };

    const terminateAuthentication = () => {
        setTimeout(() => {
            console.debug('[AuthService] Setting authenticating to false via terminateAuthentication.');
            setAuthenticating(false);
        }, 0);
    };

    const clearExplicitLogoutFlag = async () => {
        await AsyncStorage.removeItem("explicitLogout");
        console.debug('[AuthService] Removed "explicitLogout" flag from AsyncStorage.');
    };

    const getUserDetailsFromAPI = async (userUid: string): Promise<UserDetails | undefined> => {
        const response: Response = await apiService.getUserDetails(userUid);
        if (!response.ok) {
            console.error(`[AuthService] Failed to fetch user details for ${userUid}. Status: ${response.status}, Text: ${response.statusText}`);
            return undefined;
        } else {
            const userDetails: UserDetails = await response.json();
            console.debug(`[AuthService] Successfully fetched user details for ${userUid}.`);
            return userDetails;
        }
    };

    const checkIfIsExplicitLogout = async (): Promise<boolean> => {
        const wasExplicitLogout: string | null = await AsyncStorage.getItem("explicitLogout");
        const isExplicit = wasExplicitLogout === "true";
        console.debug(`[AuthService] "explicitLogout" flag from AsyncStorage is: ${wasExplicitLogout}. Parsed as: ${isExplicit}`);
        return isExplicit;
    };

    console.debug('[AuthService] Subscribing to onAuthStateChanged.');
    const unsubscribe = onAuthStateChanged(auth, async (user: User | null) => {
        console.debug(`[AuthService] onAuthStateChanged triggered. User UID: ${user?.uid}, User Email Verified: ${user?.emailVerified}`);
        const globalStoreState: GlobalStore = useGlobalStore.getState();
        const lastUserUid: string | undefined = globalStoreState.firebaseUser?.uid;
        const explicitLogout: boolean = await checkIfIsExplicitLogout();
        if (user) {
            console.debug(`[AuthService] User is authenticated. User UID: ${user.uid}`);
            setAuthenticating(true);
            try {
                if (lastUserUid && lastUserUid !== user.uid) {
                    console.debug(`[AuthService] Different user logged in. Previous UID: ${lastUserUid}, New UID: ${user.uid}. Clearing old user data.`);
                    await signOutFromRevenueCat();
                    await clearState(globalStoreState, true, lastUserUid);
                } else if (!lastUserUid) {
                    console.debug(`[AuthService] New user login. User UID: ${user.uid}`);
                } else {
                    console.debug(`[AuthService] Same user re-authenticated or refreshed. User UID: ${user.uid}`);
                }

                await signInToRevenueCat(user);
                useGlobalStore.setState({ firebaseUser: user });
                console.debug(`[AuthService] Updated firebaseUser in global store for UID: ${user.uid}`);
                if (!user.isAnonymous && user.emailVerified) {
                    const userDetails: UserDetails | undefined = await getUserDetailsFromAPI(user.uid);
                    if (userDetails) {
                        console.debug(`[AuthService] User details fetched for UID: ${user.uid}, updating store.`);
                        globalStoreState.update(userDetails);
                    } else {
                        console.warn(`[AuthService] No userDetails found from API for UID: ${user.uid}. Store not updated with details.`);
                    }
                } else {
                    console.debug(`[AuthService] User is anonymous. Skipping user details fetch for UID: ${user.uid}`);
                }
            } catch (error) {
                console.error("[AuthService] Error during authentication setup (user authenticated block):", error);
            } finally {
                await clearExplicitLogoutFlag();
                terminateAuthentication();
            }
        } else {
            try {
                if (explicitLogout) {
                    console.debug("[AuthService] Explicit logout detected. Clearing whole store and signing out from RevenueCat.");
                    await signOutFromRevenueCat();
                    await clearExplicitLogoutFlag(); // Clear flag after processing
                    await clearState(globalStoreState, true, lastUserUid);
                    console.debug("[AuthService] Store cleared after explicit logout.");
                } else {
                    console.debug("[AuthService] Passive logout or unverified email. Clearing state except pharmacies.");
                    if (lastUserUid) {
                        console.debug(`[AuthService] Passive logout for user ${lastUserUid}. Signing out from RevenueCat.`);
                        await signOutFromRevenueCat();
                    }
                    await clearState(globalStoreState, false, lastUserUid);
                    console.debug("[AuthService] State (except pharmacies) cleared after passive logout/unverified email.");
                }
            } catch (error) {
                console.error("[AuthService] Error during sign out or state clearing (user not authenticated block):", error);
            } finally {
                if (!explicitLogout) {
                    terminateAuthentication();
                }
            }
        }
    });

    console.debug('[AuthService] onAuthStateChanged listener attached.');

    // Cleanup function
    return () => {
        console.debug('[AuthService] Unsubscribing from onAuthStateChanged.');
        unsubscribe();
        isInitialized = false; // Reset initialization flag
        console.debug('[AuthService] Auth service cleanup complete.');
    };
}

/**
 * Signs out the current user from all services
 * This initiates the sign-out process, while onAuthStateChanged handles cleanup
 */
export async function signOutUser(): Promise<void> {
    console.debug('[AuthService] signOutUser function called.');
    try {
        console.debug('[AuthService] Setting "explicitLogout" flag to "true" in AsyncStorage.');
        await AsyncStorage.setItem("explicitLogout", "true");
        console.debug('[AuthService] Calling Firebase signOut.');
        await signOut(auth);
        console.debug('[AuthService] Firebase signOut successful. onAuthStateChanged will handle the rest.');
        // No need for Promise.resolve() here, async functions return a promise by default
    } catch (error) {
        console.error("[AuthService] Sign out failed:", error);
        // Attempt to clear the flag even if sign out fails, to prevent inconsistent state
        try {
            await AsyncStorage.removeItem("explicitLogout");
            console.debug('[AuthService] Cleared "explicitLogout" flag after sign out error.');
        } catch (clearError) {
            console.error('[AuthService] Failed to clear "explicitLogout" flag after sign out error:', clearError);
        }
        throw error;
    }
}