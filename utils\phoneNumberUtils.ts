import { parsePhoneNumberFromString, CountryCallingCode, CountryCode } from 'libphonenumber-js';

const cleanInput = (value: string): string => {
    return value.trim().replace(/[\s\-().]/g, '');
};

export const normalizePhoneNumberDigitsOnly = (value: string, defaultCallingCode: CountryCallingCode): string | null => {
    if (!value) return null;

    const input = cleanInput(value);

    if (input.startsWith('+')) {
        const phoneNumber = parsePhoneNumberFromString(input);
        if (phoneNumber && phoneNumber.isValid()) {
            return phoneNumber.number.replace('+', '');
        }
        return null;
    }

    // 1. Try parse assuming input is international without plus
    let phoneNumber = parsePhoneNumberFromString('+' + input);
    if (phoneNumber && phoneNumber.isValid()) {
        return phoneNumber.number.replace('+', '');
    }

    // 2. Try parse as national number with default country
    phoneNumber = parsePhoneNumberFromString(input, { defaultCallingCode });
    if (phoneNumber && phoneNumber.isValid()) {
        return phoneNumber.number.replace('+', '');
    }

    return null;
};

export const normalizeAndValidatePhoneNumber = (value: string, defaultCallingCode: CountryCallingCode): boolean => {
    if (!value) return true;

    const input = cleanInput(value);

    if (input.startsWith('+')) {
        const phoneNumber = parsePhoneNumberFromString(input);
        return phoneNumber?.isValid() ?? false;
    }

    // 1. Try parse as international number without plus
    let phoneNumber = parsePhoneNumberFromString('+' + input);
    if (phoneNumber && phoneNumber.isValid()) {
        return true;
    }

    // 2. Try parse as national number with default country
    phoneNumber = parsePhoneNumberFromString(input, { defaultCallingCode });
    return phoneNumber?.isValid() ?? false;
};
