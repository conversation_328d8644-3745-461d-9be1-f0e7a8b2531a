import { CountryCallingCode } from "libphonenumber-js";
import * as Yup from 'yup';
import { FIELD_NAME } from "../models/field";
import { normalizeAndValidatePhoneNumber } from "../utils/phoneNumberUtils";
import { TranslationService } from "./translationService";

export interface FieldDetails {
    readonly key: string;
    readonly required: boolean;
    readonly multiline: boolean;
    readonly label: string;
}

export interface SignUpFormValues {
    name: string;
    whatsappNumber?: string | null;
    password: string;
    email: string;
}

export interface UserPersonalInfoEditForm {
    name: string;
    whatsappNumber?: string | null;
}

export class YupFormsService {

    public readonly getSignUpForm: (countryCallingCode: CountryCallingCode) => Yup.ObjectSchema<SignUpFormValues>;
    public readonly getUserPersonalInfoEditForm: () => Yup.ObjectSchema<UserPersonalInfoEditForm>;
    public readonly getSavedPlaceForm: () => Map<FIELD_NAME, FieldDetails>;

    constructor(readonly translationService: TranslationService) {

        const requiredFieldLabel: string = translationService.translate("REQUIRED_FIELD");

        this.getSignUpForm = (countryCallingCode: CountryCallingCode) => {
            return Yup.object({
                name: Yup.string()
                    .required(requiredFieldLabel)
                    .nonNullable(),
                password: Yup.string()
                    .min(6, translationService.translate("PASSWORD_MIN_6_CHARACTERS_ERROR"))
                    .required(requiredFieldLabel)
                    .nonNullable(),
                email: Yup.string()
                    .email(translationService.translate("EMAIL_VALID_ERROR"))
                    .required(requiredFieldLabel)
                    .nonNullable(),
                whatsappNumber: Yup.string()
                    .notRequired()
                    .nullable()
                    .test('is-valid-phone', translationService.translate("WHATSAPP_NUMBER_NOT_VALID"), function (val) {
                        if (!val || (val && val.trim() === `+${countryCallingCode.toString()}`)) return true;
                        return normalizeAndValidatePhoneNumber(val, countryCallingCode);
                    }),
            });
        };

        this.getUserPersonalInfoEditForm = () => {
            return Yup.object({
                name: Yup.string()
                    .required(requiredFieldLabel)
                    .nonNullable(),
                whatsappNumber: Yup.string()
                    .matches(/^\d+$/, translationService.translate("ONLY_NUMBER_ERROR"))
                    .min(10, translationService.translate('WHATSAPP_NUMBER_MIN_10_DIGITS_ERROR'))
                    .notRequired()
                    .nullable()
            });
        };

        this.getSavedPlaceForm = (): Map<FIELD_NAME, FieldDetails> => new Map<FIELD_NAME, FieldDetails>([
            [FIELD_NAME.NAME, {
                key: FIELD_NAME.NAME.toString(),
                required: true,
                multiline: false,
                label: translationService.translate("FIELD_NAME")
            }],
            [FIELD_NAME.NOTE, {
                key: FIELD_NAME.NOTE.toString(),
                required: false,
                multiline: true,
                label: translationService.translate("FIELD_NOTE")
            }]
        ]);
    }
};