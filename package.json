{"name": "pharma-chainage", "main": "node_modules/expo/AppEntry.js", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll"}, "dependencies": {"@expo/metro-config": "~0.18.11", "@expo/react-native-action-sheet": "~4.0.1", "@expo/vector-icons": "^14.0.3", "@react-google-maps/api": "~2.19.2", "@react-native-async-storage/async-storage": "~1.23.1", "@react-navigation/bottom-tabs": "~6.5.9", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "~6.1.6", "@react-navigation/native-stack": "~6.9.12", "@react-navigation/stack": "~6.3.16", "@turf/turf": "^7.2.0", "@types/haversine": "^1.1.8", "@types/supercluster": "^7.1.3", "date-fns": "~3.6.0", "date-fns-tz": "^3.1.3", "dotenv": "~16.4.1", "expo": "~51.0.38", "expo-build-properties": "~0.12.5", "expo-constants": "~16.0.2", "expo-crypto": "~13.0.2", "expo-dev-client": "~4.0.28", "expo-font": "~12.0.10", "expo-linking": "~6.3.1", "expo-localization": "~15.0.3", "expo-location": "~17.0.1", "expo-network": "~6.0.1", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.6", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-updates": "~0.25.27", "firebase": "~10.8.0", "formik": "^2.4.6", "haversine": "^1.1.1", "libphonenumber-js": "^1.12.10", "lodash": "^4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-country-picker-modal": "^2.0.0", "react-native-gesture-handler": "~2.16.1", "react-native-keyboard-spacer": "~0.4.1", "react-native-maps": "1.14.0", "react-native-paper": "~5.11.5", "react-native-purchases": "^8.9.4", "react-native-purchases-ui": "^8.9.4", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-vector-icons": "~10.0.3", "react-native-web": "~0.19.6", "react-navigation-header-buttons": "~12.0.0", "supercluster": "^8.0.1", "true-myth": "~7.1.0", "use-places-autocomplete": "~4.0.1", "yup": "^1.4.0", "zustand": "~4.5.1"}, "devDependencies": {"@babel/core": "^7.24.0", "@testing-library/react-native": "^13.2.0", "@types/lodash": "^4.17.4", "@types/react": "~18.2.0", "@types/react-native-vector-icons": "~6.4.18", "jest": "^29.4.0", "jest-expo": "~51.0.4", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "overrides": {"react-refresh": "~0.14.0"}, "resolutions": {"react-refresh": "~0.14.0"}, "private": true}