import { Platform } from 'react-native';
import Purchases, { CustomerInfo, LOG_LEVEL, PurchasesEntitlementInfo } from 'react-native-purchases';
import RevenueCatUI, { PAYWALL_RESULT } from "react-native-purchases-ui";

const REVENUECAT_API_KEY_ANDROID = "goog_fmxbVVuxoOLZzNSjDDezvTGVuIS";
const REVENUECAT_API_KEY_APPLE = "appl_lKTZOrWsHIJHNnLbneXWXVmXADF";
const REQUIRED_ENTITLEMENT_IDENTIFIER = "pro";

export class RevenueCatService {

    public readonly initialize: () => Promise<void>;
    public readonly login: (userId: string) => Promise<void>;
    public readonly logout: () => Promise<void>;
    public readonly presentPaywall: () => Promise<PAYWALL_RESULT | null>;
    public readonly presentPaywallIfNeeded: (params?: { requiredEntitlementIdentifier?: string }) => Promise<PAYWALL_RESULT | null>;
    public readonly presentCustomerCenter: () => Promise<void>;
    public readonly hasActiveEntitlement: (entitlementIdentifier?: string) => Promise<{ active: boolean; entitlementInfo: PurchasesEntitlementInfo | null }>;

    constructor() {
        console.debug('[RevenueCatService] Constructor called.');

        this.initialize = async (): Promise<void> => {
            console.debug('[RevenueCatService] initialize method called.');

            console.debug('[RevenueCatService] Initializing...');
            if (__DEV__) {
                // In development, set up verbose logging from the RevenueCat SDK itself.
                Purchases.setLogHandler((level, message) => {
                    console.debug(`[RevenueCatSDK] ${message}`);
                });
                Purchases.setLogLevel(LOG_LEVEL.DEBUG); // Use DEBUG for more verbose SDK logs in DEV
                console.debug('[RevenueCatService] RevenueCat SDK log handler and log level set to DEBUG for development.');
            }

            try {
                console.debug('[RevenueCatService] Configuring Purchases SDK...');
                await Purchases.configure({
                    apiKey: Platform.OS === 'ios' ? REVENUECAT_API_KEY_APPLE : REVENUECAT_API_KEY_ANDROID,
                });
                console.debug('[RevenueCatService] Purchases SDK configured successfully.');
            } catch (error) {
                console.error('[RevenueCatService] FATAL: Error during Purchases.configure:', error);
                throw error; // Critical error, re-throw
            }
        };

        this.login = async (userId: string): Promise<void> => {
            console.debug(`[RevenueCatService] login method called with userId: ${userId}`);
            if (!userId) {
                console.warn('[RevenueCatService] login called with null or empty userId. Skipping.');
                return;
            }

            try {
                const currentAppUserID: string = await Purchases.getAppUserID();
                const isAnonymous: boolean = await Purchases.isAnonymous();
                console.debug(`[RevenueCatService] Current RC AppUserID: ${currentAppUserID}, IsAnonymous: ${isAnonymous}`);

                // If the current user is not anonymous, and their ID is different from the new ID, log them out first.
                if (!isAnonymous && currentAppUserID && currentAppUserID !== userId) {
                    console.debug(`[RevenueCatService] Identified user mismatch (${currentAppUserID} vs ${userId}). Logging out previous RC user.`);
                    await Purchases.logOut();
                    console.debug('[RevenueCatService] Previous RC user logged out.');
                } else if (currentAppUserID === userId && !isAnonymous) {
                    console.debug(`[RevenueCatService] User ${userId} is already logged into RC. Ensuring consistency.`);
                    // Potentially just return or refresh customer info if needed.
                    // For now, proceeding with logIn call as it handles identity and fetches latest data.
                }

                console.debug(`[RevenueCatService] Calling Purchases.logIn with userId: ${userId}`);
                const loginResult = await Purchases.logIn(userId);
                console.debug(`[RevenueCatService] Successfully logged into RevenueCat. User Created: ${loginResult.created}, AppUserID: ${loginResult.customerInfo.originalAppUserId}`);
            } catch (error) {
                console.error('[RevenueCatService] RevenueCat login error:', error);
                throw error;
            }
        };

        this.logout = async (): Promise<void> => {
            console.debug('[RevenueCatService] logout method called.');

            try {
                const isAnonymous: boolean = await Purchases.isAnonymous();
                if (!isAnonymous) {
                    const appUserId: string = await Purchases.getAppUserID(); // Get AppUserID for logging
                    console.debug(`[RevenueCatService] Identified user ${appUserId} found. Calling Purchases.logOut.`);
                    await Purchases.logOut();
                    console.debug('[RevenueCatService] Successfully logged out from RevenueCat.');
                } else {
                    console.debug('[RevenueCatService] User is anonymous. Skipping explicit RevenueCat logout.');
                }
            } catch (error) {
                console.error('[RevenueCatService] RevenueCat logout error:', error);
                throw error;
            }
        };

        this.presentPaywall = async (): Promise<PAYWALL_RESULT | null> => {
            console.debug('[RevenueCatService] presentPaywall method called.');

            try {
                console.debug('[RevenueCatService] Presenting paywall...');
                const result: PAYWALL_RESULT = await RevenueCatUI.presentPaywall();
                console.debug('[RevenueCatService] presentPaywall result:', result);
                return result;
            } catch (error) {
                console.error('[RevenueCatService] RevenueCat presentPaywall error:', error);
                return null;
            }
        };

        this.presentPaywallIfNeeded = async (params?: { requiredEntitlementIdentifier?: string }): Promise<PAYWALL_RESULT | null> => {
            const entitlementId = params?.requiredEntitlementIdentifier || REQUIRED_ENTITLEMENT_IDENTIFIER;
            console.debug(`[RevenueCatService] presentPaywallIfNeeded called for entitlement: ${entitlementId}`);

            try {
                console.debug(`[RevenueCatService] Presenting paywall if needed for entitlement: ${entitlementId}...`);
                const result: PAYWALL_RESULT = await RevenueCatUI.presentPaywallIfNeeded({
                    requiredEntitlementIdentifier: entitlementId
                });
                console.debug(`[RevenueCatService] presentPaywallIfNeeded result for ${entitlementId}:`, result);
                return result;
            } catch (error) {
                console.error(`[RevenueCatService] RevenueCat presentPaywallIfNeeded error for ${entitlementId}:`, error);
                return null;
            }
        };

        this.presentCustomerCenter = async (): Promise<void> => {
            console.debug('[RevenueCatService] presentCustomerCenter method called.');
            try {
                console.debug('[RevenueCatService] Presenting customer center...');
                await RevenueCatUI.presentCustomerCenter();
                console.debug('[RevenueCatService] Customer center presented (or dismissed).');
            } catch (error) {
                console.error('[RevenueCatService] RevenueCat presentCustomerCenter error:', error);
                throw error;
            }
        };

        this.hasActiveEntitlement = async (entitlementIdentifier?: string): Promise<{ active: boolean; entitlementInfo: PurchasesEntitlementInfo | null }> => {
            const targetEntitlement = entitlementIdentifier || REQUIRED_ENTITLEMENT_IDENTIFIER;
            console.debug(`[RevenueCatService] hasActiveEntitlement called for: ${targetEntitlement}`);

            try {
                console.debug(`[RevenueCatService] Fetching customer info for entitlement check: ${targetEntitlement}.`);
                const customerInfo: CustomerInfo = await Purchases.getCustomerInfo();
                // console.debug('[RevenueCatService] CustomerInfo received:', customerInfo); // Can be verbose, uncomment if needed for deep debugging

                const activeEntitlement: PurchasesEntitlementInfo | undefined = customerInfo.entitlements.active[targetEntitlement];

                if (activeEntitlement) {
                    console.debug(`[RevenueCatService] Active entitlement "${targetEntitlement}" FOUND.`);
                    return { active: true, entitlementInfo: activeEntitlement };
                } else {
                    console.debug(`[RevenueCatService] Active entitlement "${targetEntitlement}" NOT found.`);
                    return { active: false, entitlementInfo: null };
                }
            } catch (error) {
                console.error(`[RevenueCatService] RevenueCat hasActiveEntitlement error for ${targetEntitlement}:`, error);
                throw error; // Re-throw to indicate failure in checking entitlement
            }
        };
    }
}
