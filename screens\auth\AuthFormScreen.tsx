import { StackScreenProps } from '@react-navigation/stack';
import * as Linking from 'expo-linking';
import { getAuth, signInAnonymously, signInWithEmailAndPassword, signOut } from "firebase/auth";
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { Alert, Platform, StyleSheet, View } from 'react-native';
import { Button, Divider, Text, TextInput } from "react-native-paper";
import { revenueCatServiceContext, translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import { WEBSITE_URL, WHATSAPP_NUMBER } from '../../utils/constants';

import { RevenueCatService } from '../../services/revenueCatService';
import useGlobalStore from '../../services/globalState';

export const WHATSAPP_REACTIVATION_MSG: string = "Bonjour, je souhaiterais réactiver mon compte.";
export const WHATSAPP_SIGNUP_MSG: string = "Bonjour, je souhaite m'inscrire à l'application.";

const AuthFormScreen: React.FC<StackScreenProps<any>> = ({ navigation }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const emailLabel: string = translationService.translate("SHORT_EMAIL");
    const passwordLabel: string = translationService.translate("PASSWORD");
    const signInLabel: string = translationService.translate("SIGN_IN");
    const signUpLabel: string = translationService.translate("PRE_SIGN_UP");
    const dontHaveAccountLabel: string = translationService.translate("DONT_HAVE_ACCOUNT");
    const forgotYourPasswordLabel: string = translationService.translate("FORGOT_YOUR_PASSWORD");


    const [email, setEmail] = useState<string>("");
    const [password, setPassword] = useState<string>("");

    const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
    const [signingIn, setSigningIn] = useState<boolean>(false);
    const [signingInAnonymously, setSigningInAnonymously] = useState<boolean>(false);

    const setAuthLoading = useGlobalStore(state => state.setAuthLoading);
    const authenticating: boolean = useGlobalStore(state => state.authenticating);

    const auth = getAuth();

    // Reset local loading states when global authentication state changes
    useEffect(() => {
        if (!authenticating) {
            setSigningIn(false);
            setSigningInAnonymously(false);
        }
    }, [authenticating]);

    const onSignUpPress = () => {
        navigation.navigate("AuthSignUpForm");
    };

    const onSignInAnonymously = () => {
        setSigningInAnonymously(true);
        setAuthLoading(true);
        signOut(auth)
            .then(() => signInAnonymously(auth))
            .catch((error) => {
                setAuthLoading(false);
                console.error(error);
            })
            .finally(() => {
                setSigningInAnonymously(false);
            })
    };

    const signIn = () => {
        setSigningIn(true);
        setAuthLoading(true);
        signOut(auth)
            .then(() => {
                return signInWithEmailAndPassword(auth, email, password)
                    .then(async () => {
                        // User will be automatically routed to EmailVerificationScreen
                        // if email is not verified due to navigation logic in verifiedUserStack.tsx
                    })
                    .catch((error) => {
                        setAuthLoading(false);
                        if (_.includes(['auth/invalid-email', 'auth/invalid-credential', 'auth/invalid-password'], error.code)) {
                            Alert.alert(
                                translationService.translate("INVALID_CREDENTIALS"),
                                translationService.translate("INVALID_CREDENTIALS_DETAILS"),
                                [
                                    {
                                        text: translationService.translate("RETRY"),
                                    }
                                ]
                            );
                        } else if ('auth/user-disabled' === error.code) {
                            Alert.alert(
                                translationService.translate("ACCOUNT_DEACTIVATED"),
                                translationService.translate("ACCOUNT_DEACTIVATED_DETAILS"),
                                [
                                    {
                                        text: translationService.translate("OK"),
                                    },
                                    {
                                        text: translationService.translate("VISIT_OUR_WEBSITE"),
                                        onPress: () => {
                                            Linking.openURL(WEBSITE_URL);
                                        },
                                    },
                                    {
                                        text: translationService.translate("ACCOUNT_DEACTIVATED_RETRY"),
                                        onPress: () => {
                                            Linking.openURL(`https://wa.me/${WHATSAPP_NUMBER}?text=${WHATSAPP_REACTIVATION_MSG}`);
                                        },
                                    }
                                ]
                            );
                        } else if ('auth/too-many-requests' === error.code) {
                            Alert.alert(
                                translationService.translate("TOO_MANY_REQUESTS"),
                                translationService.translate("TOO_MANY_REQUESTS_DETAILS"),
                                [
                                    {
                                        text: translationService.translate("OK"),
                                    }
                                ]
                            );
                        } else if ('auth/network-request-failed' === error.code) {
                            Alert.alert(
                                translationService.translate("NETWORK_ERROR"),
                                translationService.translate("NETWORK_ERROR_DETAILS"),
                                [
                                    {
                                        text: translationService.translate("OK")
                                    }
                                ]
                            );
                        } else {
                            console.error(error);
                        }
                    })
                    .finally(() => {
                        setSigningIn(false);
                    })
            });
    };

    return (
        <View style={styles.formContainer}>
            <View style={styles.inputsContainer}>
                <TextInput
                    testID="authEmailInput"
                    mode="outlined"
                    autoCapitalize="none"
                    keyboardType="email-address"
                    autoCorrect={false}
                    label={emailLabel}
                    value={email}
                    onChangeText={setEmail}
                />
                <View style={styles.passwordContainer}>
                    <TextInput
                        testID="authPasswordInput"
                        mode="outlined"
                        label={passwordLabel}
                        value={password}
                        secureTextEntry={!passwordVisible}
                        right={<TextInput.Icon
                            icon={passwordVisible ? 'eye-off' : 'eye'}
                            onPress={() => setPasswordVisible(!passwordVisible)}
                        />}
                        onChangeText={setPassword}
                    />
                    <Text
                        variant="bodyMedium"
                        style={styles.forgotYourPassword}
                        onPress={() => { navigation.navigate('AuthPasswordReset') }}
                    >
                        {forgotYourPasswordLabel}
                    </Text>
                </View>

            </View>

            <Button
                testID="authSignInButton"
                mode="outlined"
                onPress={signIn}
                loading={signingIn}
                disabled={signingInAnonymously}
            >
                {signInLabel}
            </Button>
            {Platform.OS === 'ios' && (
                <Button
                    testID="authGuestModeButton"
                    mode="outlined"
                    loading={signingInAnonymously}
                    disabled={signingIn}
                    onPress={onSignInAnonymously}
                >
                    {translationService.translate("GUEST_MODE")}
                </Button>
            )}
            <View style={styles.signUpContainer}>
                <Divider />
                <Text style={styles.textAlignCenter}>{dontHaveAccountLabel}</Text>
                <View style={styles.passwordContainer}>
                    <Button
                        testID="authSignUpButton"
                        mode="contained"
                        onPress={onSignUpPress}
                        disabled={signingIn || signingInAnonymously}
                    >
                        {signUpLabel}
                    </Button>
                    <Text variant="bodySmall" style={[styles.textAlignCenter, styles.preSignUpHelperText]}>
                        {translationService.translate("PRE_SIGN_UP_BUTTON_HINT")}
                    </Text>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    textAlignCenter: {
        textAlign: 'center'
    },
    passwordContainer: {
        gap: 8
    },
    forgotYourPassword: {
        textDecorationLine: "underline"
    },
    formContainer: {
        flexDirection: 'column',
        gap: 16,
        padding: 16,
    },
    inputsContainer: {
        gap: 8
    },
    signUpContainer: {
        gap: 12
    },
    preSignUpHelperText: {
        color: '#888'
    }
});

export default AuthFormScreen;
